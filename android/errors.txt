A Dart VM Service on localhost:54762 is available at: http://127.0.0.1:55246/CFeyQspHGGk=/
D/ProfileInstaller( 3096): Installing profile for com.eddars.rockpaperscissors
The Flutter DevTools debugger and profiler on localhost:54762 is available at:
http://127.0.0.1:9101?uri=http://127.0.0.1:55246/CFeyQspHGGk=/
I/flutter ( 3096): 📊 IMAGE_CACHE: Memory monitoring - Cache: 46%, Memory: 87%
I/flutter ( 3096): 🚨 IMAGE_CACHE: Critical memory pressure detected! Performing emergency cleanup
I/flutter ( 3096): 🚨 IMAGE_CACHE: EMERGENCY CLEANUP - Critical memory pressure!
I/flutter ( 3096): 🔄 IMAGE_CACHE: Preloading essential images synchronously...
I/flutter ( 3096): ✅ IMAGE_CACHE: Preloaded essential: assets/images/you_win.png
I/flutter ( 3096): ✅ IMAGE_CACHE: Preloaded essential: assets/images/you_lose.png
I/flutter ( 3096): ✅ IMAGE_CACHE: Preloaded essential: assets/images/egal.png
I/flutter ( 3096): ✅ IMAGE_CACHE: Preloaded essential: assets/images/sheriff.png
I/flutter ( 3096): ✅ IMAGE_CACHE: Preloaded essential: assets/images/ciseaux.png
I/flutter ( 3096): ✅ IMAGE_CACHE: Preloaded essential: assets/images/pierre.png
I/flutter ( 3096): ✅ IMAGE_CACHE: Preloaded essential: assets/images/papier.png
I/flutter ( 3096): ✅ IMAGE_CACHE: Preloaded essential: assets/images/gameover.png
I/flutter ( 3096): ✅ IMAGE_CACHE: Preloaded essential: assets/images/play.png
I/flutter ( 3096): ✅ IMAGE_CACHE: Preloaded essential: assets/images/quit.png
I/flutter ( 3096): 🚨 IMAGE_CACHE: Emergency cleanup completed - cache cleared and essential images reloaded
V/MediaPlayer-JNI( 3096): native_finalize
V/MediaPlayer-JNI( 3096): release
I/flutter ( 3096): 🎮 GAME: Starting camera in background before countdown
I/flutter ( 3096): 🎮 GAME: Starting gesture detection in GAME mode
I/flutter ( 3096): 🎮 CAMERA: Starting detection with mode: GAME, minScore: 0.65, useFrontCamera: true
I/flutter ( 3096): 🎮 DEVICE: Running on android AP3A.240905.015.A2.S938U1UEU2AYD9
I/flutter ( 3096): 🔴 CAMERA: Error checking emulator status: MissingPluginException(No implementation found for method isEmulator on channel mp_gesture/methods)
D/MpGestureManager( 3096): 🔄 CAMERA: Changement de caméra demandé: true -> true, caméra en cours: false
D/MpGestureManager( 3096): 🚀 START: Method called
D/MpGestureManager( 3096): 📷 INFO: Configuration actuelle - useFrontCamera: true, minScore: 0.65
D/MpGestureManager( 3096): 🔍 TENTATIVE: Vérification des permissions et démarrage de la caméra...
D/MpGestureManager( 3096): ✅ PERMISSION: Camera permission granted, starting camera...
D/MpGestureManager( 3096): 📁 MODEL: Loading MediaPipe gesture recognizer...
F/libc    ( 3096): Fatal signal 11 (SIGSEGV), code 1 (SEGV_MAPERR), fault addr 0x34dfc3e6145dc in tid 3096 (ckpaperscissors), pid 3096 (ckpaperscissors)
*** *** *** *** *** *** *** *** *** *** *** *** *** *** *** ***
Build fingerprint: 'samsung/pa3quew/pa3q:15/AP3A.240905.015.A2/S938U1UEU2AYD9_OYM2AYD9:user/release-keys'
Revision: '11'
ABI: 'arm64'
Processor: '6'
Timestamp: 2025-09-15 12:12:19.110875438-0700
Process uptime: 56s
Cmdline: com.eddars.rockpaperscissors
pid: 3096, tid: 3096, name: ckpaperscissors  >>> com.eddars.rockpaperscissors <<<
uid: 10356
tagged_addr_ctrl: 0000000000000001 (PR_TAGGED_ADDR_ENABLE)
pac_enabled_keys: 000000000000000f (PR_PAC_APIAKEY, PR_PAC_APIBKEY, PR_PAC_APDAKEY, PR_PAC_APDBKEY)
signal 11 (SIGSEGV), code 1 (SEGV_MAPERR), fault addr 0x00034dfc3e6145dc
    x0  00000000d2801168  x1  00034dfc3e6145dc  x2  0000007fe193ba20  x3  00034dfc3e6145db
    x4  000000794cba9888  x5  000000794cba9828  x6  000000794be2a1c8  x7  000000794be2a000
    x8  0000000000004005  x9  0000000000000c18  x10 0000000000000c18  x11 000000794be2a0b0
    x12 0000000000000000  x13 0000000000000001  x14 ffffffffffffffff  x15 0000000000000000
    x16 0000000000000001  x17 0000007c3e60f0e8  x18 00000000000003fd  x19 0000007fe193bf40
    x20 0000007fe193bb80  x21 0000000000000000  x22 000000794c417a64  x23 0000000000000000
    x24 0000007c3e54bd12  x25 0000007c3e6d1000  x26 0000007c3e6c9000  x27 000000000000021c
    x28 000000794bf28610  x29 0000007fe193ba50
    lr  000000794c6bfd70  sp  0000007fe193ba50  pc  000000794c6bfd84  pst 0000000000001000
95 total frames
backtrace:
      #00 pc 0000000000895d84  /data/app/~~9S0B76po6xW7dHSk78IGZw==/com.eddars.rockpaperscissors-yXn2KtbKH6Yv9B3Eo4Z_1w==/lib/arm64/libmediapipe_tasks_vision_jni.so
      #01 pc 0000000000897198  /data/app/~~9S0B76po6xW7dHSk78IGZw==/com.eddars.rockpaperscissors-yXn2KtbKH6Yv9B3Eo4Z_1w==/lib/arm64/libmediapipe_tasks_vision_jni.so
      #02 pc 00000000005ed9ac  /data/app/~~9S0B76po6xW7dHSk78IGZw==/com.eddars.rockpaperscissors-yXn2KtbKH6Yv9B3Eo4Z_1w==/lib/arm64/libmediapipe_tasks_vision_jni.so
      #03 pc 00000000000eef60  /apex/com.android.runtime/bin/linker64 (__dl__ZN6soinfo17call_constructorsEv+616) (BuildId: 8fff9070c32a10dc0c62a853ef36c1c8)
      #04 pc 00000000000d65d8  /apex/com.android.runtime/bin/linker64 (__dl__Z9do_dlopenPKciPK17android_dlextinfoPKv+2796) (BuildId: 8fff9070c32a10dc0c62a853ef36c1c8)
      #05 pc 00000000000d0d20  /apex/com.android.runtime/bin/linker64 (__loader_android_dlopen_ext+76) (BuildId: 8fff9070c32a10dc0c62a853ef36c1c8)
      #06 pc 000000000000410c  /apex/com.android.runtime/lib64/bionic/libdl.so (android_dlopen_ext+20) (BuildId: 57927e0a59af83dab12a35f04503a21b)
      #07 pc 0000000000019fb8  /apex/com.android.art/lib64/libnativeloader.so (android::NativeLoaderNamespace::Load(char const*) const+140) (BuildId: 7db588114f55294ac990cf0e933b7758)
      #08 pc 00000000000087bc  /apex/com.android.art/lib64/libnativeloader.so (OpenNativeLibrary+536) (BuildId: 7db588114f55294ac990cf0e933b7758)
      #09 pc 000000000065d188  /apex/com.android.art/lib64/libart.so (art::JavaVMExt::LoadNativeLibrary(_JNIEnv*, std::__1::basic_string<char, std::__1::char_traits<char>, std::__1::allocator<char>> const&, _jobject*, _jclass*, std::__1::basic_string<char, std::__1::char_traits<char>, std::__1::allocator<char>>*)+604) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #10 pc 0000000000005914  /apex/com.android.art/lib64/libopenjdkjvm.so (JVM_NativeLoad+356) (BuildId: ee01ddb5e82126991cafcbd31e55fd49)
      #11 pc 00000000000d4aa0  [anon_shmem:dalvik-jit-code-cache] (offset 0x2000000) (art_jni_trampoline+176)
      #12 pc 000000000001fb98  [anon_shmem:dalvik-jit-code-cache] (offset 0x2000000) (java.lang.Runtime.loadLibrary0+408)
      #13 pc 000000000001f300  [anon_shmem:dalvik-jit-code-cache] (offset 0x2000000) (java.lang.Runtime.loadLibrary0+160)
      #14 pc 000000000001f090  [anon_shmem:dalvik-jit-code-cache] (offset 0x2000000) (java.lang.System.loadLibrary+160)
      #15 pc 0000000000369440  /apex/com.android.art/lib64/libart.so (art_quick_invoke_static_stub+640) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #16 pc 00000000003627d8  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>(art::ArtMethod*, art::Thread*, art::ShadowFrame&, art::Instruction const*, unsigned short, bool, art::JValue*)+2048) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #17 pc 0000000000773abc  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>(art::interpreter::SwitchImplContext*)+12208) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #18 pc 0000000000381fd8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #19 pc 000000000028d91c  <anonymous:78c5041000> (com.google.mediapipe.tasks.vision.gesturerecognizer.GestureRecognizer.<clinit>+0)
      #20 pc 0000000000353f58  /apex/com.android.art/lib64/libart.so (artQuickToInterpreterBridge+1932) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #21 pc 000000000037fa98  /apex/com.android.art/lib64/libart.so (art_quick_to_interpreter_bridge+88) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #22 pc 0000000000369440  /apex/com.android.art/lib64/libart.so (art_quick_invoke_static_stub+640) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #23 pc 0000000000446064  /apex/com.android.art/lib64/libart.so (art::ClassLinker::InitializeClass(art::Thread*, art::Handle<art::mirror::Class>, bool, bool)+5260) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #24 pc 0000000000568844  /apex/com.android.art/lib64/libart.so (art::ClassLinker::EnsureInitialized(art::Thread*, art::Handle<art::mirror::Class>, bool, bool)+160) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #25 pc 00000000003628b0  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>(art::ArtMethod*, art::Thread*, art::ShadowFrame&, art::Instruction const*, unsigned short, bool, art::JValue*)+2264) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #26 pc 0000000000773abc  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>(art::interpreter::SwitchImplContext*)+12208) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #27 pc 0000000000381fd8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #28 pc 0000000000003cfc  <anonymous:7c33a11000> (com.eddars.rockpaperscissors.gesture.MpGestureManager.startCameraAndRecognizer+0)
      #29 pc 0000000000354bcc  /apex/com.android.art/lib64/libart.so (art::interpreter::Execute(art::Thread*, art::CodeItemDataAccessor const&, art::ShadowFrame&, art::JValue, bool, bool) (.__uniq.112435418011751916792819755956732575238.llvm.834205270948026532)+428) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #30 pc 0000000000362ff4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>(art::ArtMethod*, art::Thread*, art::ShadowFrame&, art::Instruction const*, unsigned short, bool, art::JValue*)+4124) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #31 pc 0000000000773abc  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>(art::interpreter::SwitchImplContext*)+12208) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #32 pc 0000000000381fd8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #33 pc 0000000000002768  <anonymous:7c33a11000> (com.eddars.rockpaperscissors.gesture.MpGestureManager.ensurePermissionAndStart+0)
      #34 pc 0000000000354bcc  /apex/com.android.art/lib64/libart.so (art::interpreter::Execute(art::Thread*, art::CodeItemDataAccessor const&, art::ShadowFrame&, art::JValue, bool, bool) (.__uniq.112435418011751916792819755956732575238.llvm.834205270948026532)+428) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #35 pc 0000000000362ff4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>(art::ArtMethod*, art::Thread*, art::ShadowFrame&, art::Instruction const*, unsigned short, bool, art::JValue*)+4124) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #36 pc 0000000000773abc  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>(art::interpreter::SwitchImplContext*)+12208) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #37 pc 0000000000381fd8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #38 pc 0000000000002b54  <anonymous:7c33a11000> (com.eddars.rockpaperscissors.gesture.MpGestureManager.start+0)
      #39 pc 0000000000354bcc  /apex/com.android.art/lib64/libart.so (art::interpreter::Execute(art::Thread*, art::CodeItemDataAccessor const&, art::ShadowFrame&, art::JValue, bool, bool) (.__uniq.112435418011751916792819755956732575238.llvm.834205270948026532)+428) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #40 pc 0000000000362ff4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>(art::ArtMethod*, art::Thread*, art::ShadowFrame&, art::Instruction const*, unsigned short, bool, art::JValue*)+4124) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #41 pc 0000000000773abc  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>(art::interpreter::SwitchImplContext*)+12208) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #42 pc 0000000000381fd8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #43 pc 00000000000005b8  <anonymous:7c3c1c0000> (com.eddars.rockpaperscissors.MainActivity.setupMethodChannel$lambda$0+0)
      #44 pc 0000000000354bcc  /apex/com.android.art/lib64/libart.so (art::interpreter::Execute(art::Thread*, art::CodeItemDataAccessor const&, art::ShadowFrame&, art::JValue, bool, bool) (.__uniq.112435418011751916792819755956732575238.llvm.834205270948026532)+428) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #45 pc 0000000000362ff4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>(art::ArtMethod*, art::Thread*, art::ShadowFrame&, art::Instruction const*, unsigned short, bool, art::JValue*)+4124) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #46 pc 0000000000773abc  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>(art::interpreter::SwitchImplContext*)+12208) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #47 pc 0000000000381fd8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #48 pc 0000000000000504  <anonymous:7c3c1c0000> (com.eddars.rockpaperscissors.MainActivity.$r8$lambda$u0q3KbPXz3bRc8xQwPPpVcG4ppg+0)
      #49 pc 0000000000354bcc  /apex/com.android.art/lib64/libart.so (art::interpreter::Execute(art::Thread*, art::CodeItemDataAccessor const&, art::ShadowFrame&, art::JValue, bool, bool) (.__uniq.112435418011751916792819755956732575238.llvm.834205270948026532)+428) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #50 pc 0000000000362ff4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>(art::ArtMethod*, art::Thread*, art::ShadowFrame&, art::Instruction const*, unsigned short, bool, art::JValue*)+4124) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #51 pc 0000000000773abc  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>(art::interpreter::SwitchImplContext*)+12208) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #52 pc 0000000000381fd8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #53 pc 00000000000004e8  <anonymous:7c3c1c0000> (com.eddars.rockpaperscissors.MainActivity$$ExternalSyntheticLambda0.onMethodCall+0)
      #54 pc 0000000000353f58  /apex/com.android.art/lib64/libart.so (artQuickToInterpreterBridge+1932) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #55 pc 000000000037fa98  /apex/com.android.art/lib64/libart.so (art_quick_to_interpreter_bridge+88) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #56 pc 00000000003a9ad8  [anon_shmem:dalvik-jit-code-cache] (offset 0x2000000) (io.flutter.plugin.common.MethodChannel$IncomingMethodCallHandler.onMessage+280)
      #57 pc 00000000003aa854  [anon_shmem:dalvik-jit-code-cache] (offset 0x2000000) (io.flutter.embedding.engine.dart.DartMessenger.invokeHandler+276)
      #58 pc 00000000003a1430  [anon_shmem:dalvik-jit-code-cache] (offset 0x2000000) (io.flutter.embedding.engine.dart.DartMessenger.lambda$dispatchMessageToQueue$0$io-flutter-embedding-engine-dart-DartMessenger+480)
      #59 pc 00000000003a8e9c  [anon_shmem:dalvik-jit-code-cache] (offset 0x2000000) (io.flutter.embedding.engine.dart.DartMessenger$$ExternalSyntheticLambda0.run+172)
      #60 pc 0000000000298ad4  [anon_shmem:dalvik-jit-code-cache] (offset 0x2000000) (android.os.Handler.handleCallback+132)
      #61 pc 00000000000591f0  [anon_shmem:dalvik-jit-code-cache] (offset 0x2000000) (android.os.Handler.dispatchMessage+128)
      #62 pc 000000000004d9e4  [anon_shmem:dalvik-jit-code-cache] (offset 0x2000000) (android.os.Looper.loopOnce+1140)
      #63 pc 0000000000369440  /apex/com.android.art/lib64/libart.so (art_quick_invoke_static_stub+640) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #64 pc 00000000003627d8  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>(art::ArtMethod*, art::Thread*, art::ShadowFrame&, art::Instruction const*, unsigned short, bool, art::JValue*)+2048) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #65 pc 0000000000773abc  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>(art::interpreter::SwitchImplContext*)+12208) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #66 pc 0000000000381fd8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #67 pc 0000000000295bb8  /system/framework/framework.jar (android.os.Looper.loop+0)
      #68 pc 0000000000354bcc  /apex/com.android.art/lib64/libart.so (art::interpreter::Execute(art::Thread*, art::CodeItemDataAccessor const&, art::ShadowFrame&, art::JValue, bool, bool) (.__uniq.112435418011751916792819755956732575238.llvm.834205270948026532)+428) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #69 pc 0000000000362ff4  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>(art::ArtMethod*, art::Thread*, art::ShadowFrame&, art::Instruction const*, unsigned short, bool, art::JValue*)+4124) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #70 pc 0000000000773abc  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>(art::interpreter::SwitchImplContext*)+12208) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #71 pc 0000000000381fd8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #72 pc 00000000001d4c3c  /system/framework/framework.jar (android.app.ActivityThread.main+0)
      #73 pc 0000000000353f58  /apex/com.android.art/lib64/libart.so (artQuickToInterpreterBridge+1932) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #74 pc 000000000037fa98  /apex/com.android.art/lib64/libart.so (art_quick_to_interpreter_bridge+88) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #75 pc 0000000000369440  /apex/com.android.art/lib64/libart.so (art_quick_invoke_static_stub+640) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #76 pc 0000000000364e70  /apex/com.android.art/lib64/libart.so (_jobject* art::InvokeMethod<(art::PointerSize)8>(art::ScopedObjectAccessAlreadyRunnable const&, _jobject*, _jobject*, _jobject*, unsigned long)+732) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #77 pc 00000000006cc084  /apex/com.android.art/lib64/libart.so (art::Method_invoke(_JNIEnv*, _jobject*, _jobject*, _jobjectArray*) (.__uniq.165753521025965369065708152063621506277)+32) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #78 pc 000000000037f970  /apex/com.android.art/lib64/libart.so (art_quick_generic_jni_trampoline+144) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #79 pc 0000000000369174  /apex/com.android.art/lib64/libart.so (art_quick_invoke_stub+612) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #80 pc 0000000000362760  /apex/com.android.art/lib64/libart.so (bool art::interpreter::DoCall<false>(art::ArtMethod*, art::Thread*, art::ShadowFrame&, art::Instruction const*, unsigned short, bool, art::JValue*)+1928) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #81 pc 0000000000773abc  /apex/com.android.art/lib64/libart.so (void art::interpreter::ExecuteSwitchImplCpp<false>(art::interpreter::SwitchImplContext*)+12208) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #82 pc 0000000000381fd8  /apex/com.android.art/lib64/libart.so (ExecuteSwitchImplAsm+8) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #83 pc 000000000034ace0  /system/framework/framework.jar (com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run+0)
      #84 pc 0000000000353f58  /apex/com.android.art/lib64/libart.so (artQuickToInterpreterBridge+1932) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #85 pc 000000000037fa98  /apex/com.android.art/lib64/libart.so (art_quick_to_interpreter_bridge+88) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #86 pc 000000000084d47c  /system/framework/arm64/boot-framework.oat (com.android.internal.os.ZygoteInit.main+3132) (BuildId: 28913a95262b8f5bf42dae74e55a6283e5bf03d8)
      #87 pc 0000000000369440  /apex/com.android.art/lib64/libart.so (art_quick_invoke_static_stub+640) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #88 pc 00000000003548e8  /apex/com.android.art/lib64/libart.so (art::ArtMethod::Invoke(art::Thread*, unsigned int*, unsigned int, art::JValue*, char const*)+204) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #89 pc 000000000035289c  /apex/com.android.art/lib64/libart.so (art::JValue art::InvokeWithVarArgs<_jmethodID*>(art::ScopedObjectAccessAlreadyRunnable const&, _jobject*, _jmethodID*, std::__va_list)+512) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #90 pc 0000000000740ff8  /apex/com.android.art/lib64/libart.so (art::JNI<true>::CallStaticVoidMethodV(_JNIEnv*, _jclass*, _jmethodID*, std::__va_list)+104) (BuildId: 43f282f90a5e6d74413fb869838162ec)
      #91 pc 00000000000e142c  /system/lib64/libandroid_runtime.so (_JNIEnv::CallStaticVoidMethod(_jclass*, _jmethodID*, ...)+108) (BuildId: de6eeb233b63e6d92a24af8051561452)
      #92 pc 00000000000f7328  /system/lib64/libandroid_runtime.so (android::AndroidRuntime::start(char const*, android::Vector<android::String8> const&, bool)+928) (BuildId: de6eeb233b63e6d92a24af8051561452)
      #93 pc 00000000000045c8  /system/bin/app_process64 (main+1288) (BuildId: e9e9dd5710619044bf719e0aab7eb551)
      #94 pc 0000000000059ac8  /apex/com.android.runtime/lib64/bionic/libc.so (__libc_init+120) (BuildId: 9ecf995a27c885d1447a549f8c253e65)
Lost connection to device.
noury@MacBook-Pro-de-NourEddine rock_paper_scissors_flutter_FINAL % 